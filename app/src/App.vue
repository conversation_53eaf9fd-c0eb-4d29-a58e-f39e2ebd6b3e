<script setup lang="ts">
import { ref } from 'vue'
import HelloWorld from './components/HelloWorld.vue'
import TheWelcome from './components/TheWelcome.vue'
import Button from 'primevue/button'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'

const demoText = ref('')

const showMessage = () => {
  alert(`You entered: ${demoText.value || 'Nothing yet!'}`)
}
</script>

<template>
  <header>
    <img alt="Vue logo" class="logo" src="./assets/logo.svg" width="125" height="125" />

    <div class="wrapper">
      <HelloWorld msg="You did it!" />
    </div>
  </header>

  <main>
    <TheWelcome />

    <!-- PrimeVue Demo Section -->
    <div class="primevue-demo">
      <Card>
        <template #title>PrimeVue Demo</template>
        <template #content>
          <p>This is a demonstration of PrimeVue components integrated with your Vue.js application.</p>

          <div class="demo-controls">
            <InputText v-model="demoText" placeholder="Enter some text..." />
            <Button label="Click Me!" icon="pi pi-check" @click="showMessage" />
          </div>
        </template>
      </Card>
    </div>
  </main>
</template>

<style scoped>
header {
  line-height: 1.5;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }
}

.primevue-demo {
  margin: 2rem 0;
  padding: 0 1rem;
}

.demo-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .demo-controls {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
